# 导入所需的库
import time  # 用于添加延迟，避免访问过快被封禁
import random  # 用于生成随机数，模拟人类行为
import pandas as pd  # 强大的数据分析库，我们将用它来处理和保存数据

from selenium import webdriver  # 自动化测试工具，用于驱动浏览器进行网页操作
from selenium.webdriver.chrome.service import Service  # 用于管理ChromeDriver服务
from selenium.webdriver.chrome.options import Options  # 用于配置Chrome浏览器的选项
from webdriver_manager.chrome import ChromeDriverManager  # 自动管理ChromeDriver的下载和路径
from bs4 import BeautifulSoup  # 用于解析HTML网页内容，方便地提取数据


class JobScraper(object):
    """
    一个用于从招聘网站（此处以“BOSS直聘”为例）爬取职位信息的爬虫类。
    """

    def __init__(self):
        """
        类的构造函数，在创建类的实例时自动执行，用于初始化爬虫。
        """
        # --- 1. 初始化数据存储 ---
        # 创建一个空列表，用于存储所有爬取到的职位信息。
        # 每条职位信息将作为一个字典存入此列表。
        self.scraped_data = []

        # --- 2. 配置浏览器选项 (对抗反爬虫) ---
        chrome_options = Options()  # 创建Chrome选项对象
        # 无头模式，如果需要看到浏览器界面，可以注释掉下面这行
        # chrome_options.add_argument('--headless')
        # chrome_options.add_argument('--no-sandbox')  # 在Linux/Docker环境中运行时需要
        chrome_options.add_argument('--disable-dev-shm-usage')  # 解决一些资源限制问题

        # 设置一个常规的User-Agent，伪装成普通用户浏览器，防止被识别为爬虫
        chrome_options.add_argument(
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36')

        # 以下是一些高级反-反爬虫设置，旨在让Selenium更难被网站检测到
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')  # 禁用Blink运行时的自动化控制功能
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])  # 移除"Chrome正受到自动测试软件的控制"的提示
        chrome_options.add_experimental_option('useAutomationExtension', False)  # 不使用自动化扩展

        # --- 3. 初始化WebDriver ---
        try:
            # 使用webdriver_manager自动安装和设置chromedriver
            service = Service(ChromeDriverManager().install())
            # 创建Chrome浏览器驱动实例，并应用上面配置好的选项
            self.driver = webdriver.Chrome(service=service, options=chrome_options)


            # 执行JavaScript代码，将navigator.webdriver属性设置为undefined，这是隐藏Selenium身份的关键一步
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
            })
            cookies = [
                {"name": "lastCity", "value": "101020100"},
                {"name": "__zp_seo_uuid__", "value": "70eec16b-39af-4681-b8ff-49201f17a368"},
                {"name": "__l", "value": "r=https%3A%2F%2Fwww.google.com%2F&l=%2F&s=1"},
                {"name": "ab_guid", "value": "a90eab9c-f17e-452f-b682-5c15c521f99c"},
                {"name": "wt2",
                 "value": "DgDyUi7VEhdPDXNjI_cXKR9D_jhsXJwb0s2iovrYRsmmkkucWemiMDyPx-K-bmZR_nKutp1FFlAK9RoOwWwphkA~~"},
                {"name": "wbg", "value": "0"},
                {"name": "zp_at", "value": "EeU8rIKWY4tBUDfgC928DwfTWM_vPtH-uKpwRO_8mNo~"},
                {"name": "bst", "value": "V2R9ohEuz02V1pVtRuyhwZKiyy7DrRwys~|R9ohEuz02V1pVtRuyhwZKiyy7DrVzSg~"},
                {"name": "__zp_stoken__",
                 "value": "7f3afVVTDt8K%2Bwp%2FDlkkvGhoZGg1VLkJUOG5WVTBASUJVVD47QFVUVik%2BRTnCv8OGwqfCh1vDmQ5JMFQ%2BVklVSj5JVCBUQsS%2Fwr5UVUXDpsOJwrrCmmx2w44mw4jDiRrEisOCJ8SUwr4vwp7DlA3CqcK%2BQ0bClsOIPkI7SV7Cu0LDiRnCuz7DiCPCvD7DlEJTST5EViUZaCRWU1pPWxBRdk5hak0LZ2dLMUlVSFXChsK2w74yVxkjJCQYJw0aGiYaECcOEhgSJSUZJA4ZGSUsPsK2wr7CmMO%2FxJ7Dl8OsxLTCok7El1zDvFLCusOFw4PDlMSJwrBRYcKaX8OCw4bCrkvDh8OKwqp4W8OWWMKXwoXDlcKhw4F2woPDiWDClGZcw4Fqw4l9WRhoJnYRVgvDqHXDmQ%3D%3D"}
            ]
            self.driver.get("https://www.zhipin.com")
            for cookie in cookies:
                self.driver.add_cookie(cookie)
            self.driver.get("https://www.zhipin.com")
            time.sleep(2)
        except Exception as e:
            # 如果浏览器初始化失败，打印错误并抛出异常
            print(f"浏览器初始化错误: {e}")
            raise e

        # 设置隐式等待时间。如果页面元素没有立即出现，驱动会等待最多20秒再抛出错误。
        self.driver.implicitly_wait(20)

        # --- 4. 初始化爬取关键词 ---
        # 默认爬取的关键词，可以后续通过 set_keyword 方法修改
        self.keywords = ['数据分析']

    def __del__(self):
        """
        类的析构函数，在对象被销毁时自动执行。
        """
        # 关闭浏览器窗口和驱动进程，释放系统资源。
        print(">>> 爬虫结束，关闭浏览器...")
        self.driver.quit()

    def set_keywords(self, keywords):
        """
        设置要爬取的职位关键词。
        :param keywords: 可以是一个字符串（如 "Python"）或一个列表（如 ["Java", "C++"]）
        """
        self.keywords = []  # 先清空旧的关键词列表
        if isinstance(keywords, list):
            self.keywords = keywords  # 如果输入是列表，直接赋值
        else:
            # 如果输入是字符串，按空格分割成列表（如果只有一个词，也放入列表）
            self.keywords = str(keywords).strip().split()

    def get_keywords(self):
        """
        获取当前设置的关键词列表。
        """
        return self.keywords

    def random_scroll(self):
        """
        在页面内进行随机滚动，模拟用户浏览行为，可能有助于绕过一些反爬检测。
        """
        # 获取当前页面的总高度
        total_height = self.driver.execute_script("return document.body.scrollHeight")
        for _ in range(2):  # 随机滚动2次
            # 随机选择一个滚动目标位置
            target_height = random.randint(0, total_height)
            # 执行JS代码滚动到目标位置
            self.driver.execute_script(f"window.scrollTo(0, {target_height});")
            # 随机暂停0.5到1.5秒
            time.sleep(random.uniform(0.5, 1.5))

    def run(self):
        """
        爬虫主程序，执行整个爬取流程。
        """
        print("当前页面标题：", self.driver.title)

        print(">>> 开始获取职位信息...")

        # 定义要爬取的城市列表，包含城市名和在URL中使用的代码
        cities = [
            {"name": "北京", "code": 101010100},
              # {"name": "上海", "code": 101020100},
              # {"name": "广州", "code": 101280100},
              # {"name": "深圳", "code": 101280600},
              # {"name": "杭州", "code": 101210100},
              # {"name": "成都", "code": 101270100},
              # {"name": "武汉", "code": 101200100},
              # {"name": "南京", "code": 101190100}
                  ]  # 可以根据需要增删城市

        total_jobs_scraped = 0  # 记录总共爬取到的职位数量

        # --- 外层循环: 遍历所有关键词 ---
        for keyword in self.keywords:
            print(f'>>> 当前获取关键词: "{keyword}"')

            # --- 中层循环: 遍历所有城市 ---
            for city in cities:
                print(f'>>> 当前获取城市: "{city["name"]}"')
                city_job_count = 0  # 记录当前城市爬取到的职位数

                # --- 内层循环: 遍历指定页数 (这里是1到10页) ---
                for page in range(1, 2):  # BOSS直聘通常只显示前10页
                    # 构建目标URL
                    url = f'https://www.zhipin.com/web/geek/job?query={keyword}&city={city["code"]}&page={page}'

                    try:
                        # 随机暂停2到5秒，避免请求过于频繁
                        time.sleep(random.uniform(2, 5))

                        print(f"--- 正在爬取第 {page} 页: {url}")
                        # 使用Selenium驱动访问URL
                        self.driver.get(url)

                        # 随机滚动页面
                        self.random_scroll()

                        # 获取页面加载完成后的HTML源代码
                        html_source = self.driver.page_source
                        # 使用BeautifulSoup解析HTML
                        soup = BeautifulSoup(html_source, 'html.parser')

                        # 检查是否被反爬虫系统拦截（例如出现验证码页面）
                        # 通过查找一个正常页面应该有的元素来判断。这里我们查找职位列表容器。
                        # BOSS直聘新版的职位列表项class为 'job-card-wrapper'
                        job_list_items = soup.find_all('li', class_='job-card-wrapper')

                        if not job_list_items:
                            # 如果找不到职位列表，可能被拦截或此页无内容
                            print(f"--- 第 {page} 页没有找到职位信息，可能已被反爬或无更多结果。跳过此页...")
                            # 如果连续多页出现此情况，可以考虑停止对该城市的爬取
                            if page > 3 and city_job_count == 0:  # 如果前3页都没数据，可能此城市无该职位
                                print(
                                    f"--- 在城市 '{city['name']}' 的前几页未找到 '{keyword}' 相关职位，跳至下一个城市。")
                                break  # 跳出页数循环
                            continue  # 继续下一页

                        # --- 数据解析: 遍历页面上所有的职位条目 ---
                        for job_item in job_list_items:
                            # 使用.get_text(strip=True)可以获取标签内的文本并去除首尾空白
                            # 使用 '?' 安全导航操作符，即使找不到标签也不会报错，而是返回None
                            job_name = job_item.find('span', class_='job-name').get_text(strip=True) if job_item.find(
                                'span', class_='job-name') else "N/A"
                            job_area = job_item.find('span', class_='job-area').get_text(strip=True) if job_item.find(
                                'span', class_='job-area') else "N/A"
                            job_salary = job_item.find('span', class_='salary').get_text(strip=True) if job_item.find(
                                'span', class_='salary') else "N/A"
                            company_name = job_item.find('h4', class_='company-name').get_text(
                                strip=True) if job_item.find('h4', class_='company-name') else "N/A"

                            # 经验和学历信息在同一个标签列表里，需要分别提取
                            tags_list = job_item.find('ul', class_='tag-list').find_all('li') if job_item.find('ul',
                                                                                                               class_='tag-list') else []
                            job_experience = tags_list[0].get_text(strip=True) if len(tags_list) > 0 else "N/A"
                            job_education = tags_list[1].get_text(strip=True) if len(tags_list) > 1 else "N/A"

                            # 公司标签，如行业、融资情况、规模
                            company_tags = job_item.find('ul', class_='company-tag-list').find_all(
                                'li') if job_item.find('ul', class_='company-tag-list') else []
                            company_industry = company_tags[0].get_text(strip=True) if len(company_tags) > 0 else "N/A"
                            company_scale = company_tags[-1].get_text(strip=True) if len(company_tags) > 1 else "N/A"

                            # 福利标签
                            welfare_tags = job_item.find('div', class_='info-desc').get_text(
                                strip=True) if job_item.find('div', class_='info-desc') else "N/A"

                            # 将提取到的数据存入一个字典
                            job_data = {
                                "职位名称": job_name,
                                "薪资范围": job_salary,
                                "工作地点": job_area,
                                "经验要求": job_experience,
                                "学历要求": job_education,
                                "公司名称": company_name,
                                "公司行业": company_industry,
                                "公司规模": company_scale,
                                "福利待遇": welfare_tags,
                                "爬取城市": city['name'],  # 添加城市信息
                                "职位关键词": keyword  # 添加关键词信息
                            }

                            # 将该职位的字典追加到总数据列表中
                            self.scraped_data.append(job_data)
                            city_job_count += 1

                    except Exception as e:
                        print(f"爬取URL: {url} 时发生错误: {e}")
                        continue  # 即使当前页出错，也继续尝试下一页

                print(f'>>> 城市: "{city["name"]}" 获取完成... 共获取数据: {city_job_count} 条')
                total_jobs_scraped += city_job_count

        print(f">>> 所有关键词获取完成... 共获取 {total_jobs_scraped} 条数据")

        # --- 5. 保存数据到CSV文件 ---
        if self.scraped_data:  # 确保有数据才保存
            print(">>> 正在将数据保存到CSV文件...")
            # 将列表字典转换为pandas DataFrame
            df = pd.DataFrame(self.scraped_data)
            # 定义文件名
            filename = f"job_postings_raw_{'_'.join(self.keywords)}.csv"
            # 保存为CSV文件，index=False表示不保存行索引
            # encoding='utf-8-sig'是为了确保中文在Excel中打开时不会乱码
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f">>> 数据已成功保存为 {filename}")
        else:
            print(">>> 未爬取到任何数据，不生成文件。")


# --- 程序执行入口 ---
if __name__ == "__main__":
    # 1. 创建爬虫实例
    my_scraper = JobScraper()

    # 2. 设置你感兴趣的关键词 (可以是一个或多个)
    # 例如: my_scraper.set_keywords("数据可视化")
    # 例如: my_scraper.set_keywords(["数据分析师", "商业分析", "数据产品经理"])
    my_scraper.set_keywords(["数据分析"])

    # 3. 启动爬虫
    my_scraper.run()