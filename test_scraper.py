#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试爬虫脚本
用于验证修复后的爬虫是否正常工作
"""

import sys
import os
sys.path.append('src')

from 数据爬取 import JobScraper
import time

def test_scraper():
    """
    测试爬虫基本功能
    """
    scraper = None
    try:
        print("开始测试爬虫...")
        
        # 创建爬虫实例
        scraper = JobScraper()
        
        # 设置测试关键词
        scraper.set_keywords(["数据分析"])
        
        # 测试访问主页
        print("测试访问BOSS直聘主页...")
        scraper.driver.get("https://www.zhipin.com")
        time.sleep(3)
        
        print(f"页面标题: {scraper.driver.title}")
        print(f"当前URL: {scraper.driver.current_url}")
        
        # 检查页面是否正常加载
        if "BOSS直聘" in scraper.driver.title or "boss" in scraper.driver.title.lower():
            print("✓ 页面加载成功")
        else:
            print("✗ 页面加载可能有问题")
            
        # 测试搜索功能
        print("测试搜索功能...")
        search_url = "https://www.zhipin.com/web/geek/job?query=数据分析&city=101010100&page=1"
        scraper.driver.get(search_url)
        time.sleep(5)
        
        # 检查是否有职位列表
        page_source = scraper.driver.page_source
        if "job-card-wrapper" in page_source or "job-primary" in page_source:
            print("✓ 搜索页面加载成功，找到职位列表")
        else:
            print("✗ 搜索页面可能被反爬拦截")
            print("页面内容预览:")
            print(page_source[:500] + "...")
            
        print("测试完成！")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if scraper and hasattr(scraper, 'driver'):
            scraper.driver.quit()
            print("浏览器已关闭")

if __name__ == "__main__":
    test_scraper()
