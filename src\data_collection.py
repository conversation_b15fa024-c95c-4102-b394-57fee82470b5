#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集模块 - 电影数据真实爬虫
从多个真实数据源获取电影数据：豆瓣电影、时光网、猫眼等
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import json
import re
from urllib.parse import urljoin
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MovieDataCollector:
    """电影数据采集器 - 真实数据爬取"""

    def __init__(self):
        """初始化采集器"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.movies_data = []
        
    def get_douban_movies(self):
        """
        获取豆瓣电影数据（包括Top250和热门电影）
        Returns:
            list: 电影数据列表
        """
        logger.info("开始采集豆瓣电影数据...")

        # 采集豆瓣Top250
        self._get_douban_top250()

        # 采集豆瓣热门电影
        self._get_douban_popular()

        return self.movies_data

    def _get_douban_top250(self):
        """获取豆瓣电影Top250"""
        logger.info("采集豆瓣Top250...")

        for start in range(0, 250, 25):
            url = f"https://movie.douban.com/top250?start={start}&filter="

            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                soup = BeautifulSoup(response.content, 'html.parser')

                # 解析电影信息
                movie_items = soup.find_all('div', class_='item')

                for item in movie_items:
                    movie_info = self._parse_douban_movie(item, source='douban_top250')
                    if movie_info:
                        self.movies_data.append(movie_info)

                logger.info(f"已采集 {len(self.movies_data)} 部电影数据")

                # 随机延时，避免被封
                time.sleep(random.uniform(2, 4))

            except Exception as e:
                logger.error(f"采集豆瓣Top250第 {start//25 + 1} 页时出错: {e}")
                continue

    def _get_douban_popular(self):
        """获取豆瓣热门电影"""
        logger.info("采集豆瓣热门电影...")

        # 获取不同类型的热门电影
        categories = ['热门', '最新', '经典', '可播放']

        for category in categories:
            try:
                # 使用豆瓣的搜索API
                search_url = "https://movie.douban.com/j/search_subjects"
                params = {
                    'type': 'movie',
                    'tag': category,
                    'sort': 'recommend',
                    'page_limit': 20,
                    'page_start': 0
                }

                response = self.session.get(search_url, params=params, timeout=10)
                response.raise_for_status()

                data = response.json()
                subjects = data.get('subjects', [])

                for subject in subjects:
                    movie_info = self._parse_douban_json_movie(subject, source=f'douban_{category}')
                    if movie_info and not self._is_duplicate(movie_info):
                        self.movies_data.append(movie_info)

                logger.info(f"采集{category}电影 {len(subjects)} 部")
                time.sleep(random.uniform(1, 2))

            except Exception as e:
                logger.error(f"采集豆瓣{category}电影时出错: {e}")
                continue
    
    def _parse_douban_movie(self, item, source='douban'):
        """
        解析单个豆瓣电影条目（来自Top250页面）
        Args:
            item: BeautifulSoup解析的电影条目
            source: 数据来源标识
        Returns:
            dict: 电影信息字典
        """
        try:
            # 基本信息
            title_element = item.find('span', class_='title')
            title = title_element.text if title_element else ""

            # 评分
            rating_element = item.find('span', class_='rating_num')
            rating = float(rating_element.text) if rating_element else 0.0

            # 评价人数
            rating_people_element = item.find('div', class_='star').find_all('span')[-1]
            rating_people = re.findall(r'\d+', rating_people_element.text)
            rating_people = int(rating_people[0]) if rating_people else 0

            # 详细信息（年份、国家、类型）
            info_element = item.find('p', class_='')
            info_text = info_element.text.strip() if info_element else ""

            # 解析年份
            year_match = re.search(r'(\d{4})', info_text)
            year = int(year_match.group(1)) if year_match else 0

            # 解析导演和演员
            director_actor_info = info_text.split('\n')[0] if '\n' in info_text else info_text
            director = self._extract_director(director_actor_info)

            # 解析国家和类型
            details = info_text.split('\n')[-1] if '\n' in info_text else ""
            country, genres = self._extract_country_genres(details)

            # 排名
            rank_element = item.find('em', class_='')
            rank = int(rank_element.text) if rank_element else 0

            # 一句话评价
            quote_element = item.find('span', class_='inq')
            quote = quote_element.text if quote_element else ""

            # 获取电影ID用于后续获取详细信息
            link_element = item.find('a')
            movie_id = ""
            if link_element:
                href = link_element.get('href', '')
                id_match = re.search(r'/subject/(\d+)/', href)
                movie_id = id_match.group(1) if id_match else ""

            return {
                'title': title,
                'movie_id': movie_id,
                'rank': rank,
                'rating': rating,
                'rating_people': rating_people,
                'year': year,
                'director': director,
                'country': country,
                'genres': genres,
                'quote': quote,
                'source': source
            }

        except Exception as e:
            logger.error(f"解析电影信息时出错: {e}")
            return None

    def _parse_douban_json_movie(self, subject, source='douban'):
        """
        解析豆瓣JSON格式的电影数据
        Args:
            subject: JSON格式的电影数据
            source: 数据来源标识
        Returns:
            dict: 电影信息字典
        """
        try:
            return {
                'title': subject.get('title', ''),
                'movie_id': subject.get('id', ''),
                'rank': 0,  # JSON数据中没有排名
                'rating': float(subject.get('rate', 0)),
                'rating_people': 0,  # JSON数据中没有评价人数
                'year': int(subject.get('year', 0)) if subject.get('year') else 0,
                'director': ', '.join(subject.get('directors', [])),
                'country': '',  # JSON数据中没有国家信息
                'genres': ', '.join(subject.get('casts', [])),  # 这里是演员，不是类型
                'quote': subject.get('title', ''),
                'source': source
            }
        except Exception as e:
            logger.error(f"解析JSON电影信息时出错: {e}")
            return None
    
    def _extract_director(self, text):
        """提取导演信息"""
        director_match = re.search(r'导演:\s*([^主]*?)(?:\s|主演)', text)
        return director_match.group(1).strip() if director_match else ""

    def _extract_country_genres(self, text):
        """提取国家和类型信息"""
        parts = text.strip().split('/')
        if len(parts) >= 2:
            country = parts[1].strip() if len(parts) > 1 else ""
            genres = parts[2].strip() if len(parts) > 2 else ""
        else:
            country = ""
            genres = ""
        return country, genres

    def _is_duplicate(self, movie_info):
        """
        检查电影是否已存在
        Args:
            movie_info: 电影信息字典
        Returns:
            bool: 是否重复
        """
        title = movie_info.get('title', '')
        year = movie_info.get('year', 0)

        for existing_movie in self.movies_data:
            if (existing_movie.get('title', '') == title and
                existing_movie.get('year', 0) == year):
                return True
        return False
    
    def get_mtime_box_office_data(self):
        """
        从时光网获取票房数据
        """
        logger.info("开始从时光网获取票房数据...")

        try:
            # 时光网票房排行榜
            url = "http://www.mtime.com/boxoffice/"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            # 解析票房数据
            box_office_items = soup.find_all('div', class_='mov_pic')

            for item in box_office_items:
                box_office_info = self._parse_mtime_box_office(item)
                if box_office_info:
                    # 查找匹配的电影并更新票房信息
                    self._update_movie_box_office(box_office_info)

            logger.info("时光网票房数据获取完成")

        except Exception as e:
            logger.error(f"获取时光网票房数据时出错: {e}")

    def _parse_mtime_box_office(self, item):
        """
        解析时光网票房数据
        Args:
            item: BeautifulSoup解析的票房条目
        Returns:
            dict: 票房信息字典
        """
        try:
            # 电影标题
            title_element = item.find('a', class_='mov_pic')
            title = title_element.get('title', '') if title_element else ""

            # 票房数据通常在相邻的元素中
            parent = item.parent
            box_office_text = parent.get_text() if parent else ""

            # 提取票房数字（万元）
            box_office_match = re.search(r'(\d+\.?\d*)[万亿]', box_office_text)
            box_office = 0
            if box_office_match:
                box_office = float(box_office_match.group(1))
                if '亿' in box_office_text:
                    box_office *= 10000  # 转换为万元

            return {
                'title': title,
                'box_office_wan': box_office
            }

        except Exception as e:
            logger.error(f"解析时光网票房数据时出错: {e}")
            return None

    def _update_movie_box_office(self, box_office_info):
        """
        更新电影的票房信息
        Args:
            box_office_info: 票房信息字典
        """
        title = box_office_info['title']
        box_office = box_office_info['box_office_wan']

        for movie in self.movies_data:
            if self._is_same_movie(movie['title'], title):
                movie['box_office_wan'] = box_office
                movie['box_office_million'] = round(box_office / 10000, 2)
                break

    def _is_same_movie(self, title1, title2):
        """
        判断两个电影标题是否指向同一部电影
        Args:
            title1, title2: 电影标题
        Returns:
            bool: 是否为同一部电影
        """
        # 简单的字符串匹配，可以根据需要改进
        return title1.strip() == title2.strip() or title1 in title2 or title2 in title1
    
    def save_data(self, filename='raw_movie_data.csv'):
        """
        保存采集的数据到CSV文件
        Args:
            filename: 保存的文件名
        """
        if not self.movies_data:
            print("没有数据可保存")
            return
            
        df = pd.DataFrame(self.movies_data)
        
        # 确保data/raw目录存在
        os.makedirs('data/raw', exist_ok=True)
        
        filepath = os.path.join('data/raw', filename)
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        print(f"数据已保存到 {filepath}")
        print(f"共保存 {len(df)} 条电影数据")
        
        return df

    def get_additional_real_data(self):
        """
        获取额外的真实电影数据
        从电影资讯网站获取更多电影信息
        """
        logger.info("获取额外的真实电影数据...")

        try:
            # 从1905电影网获取电影资讯
            self._get_1905_movie_data()

            # 从新浪娱乐获取电影数据
            self._get_sina_movie_data()

        except Exception as e:
            logger.error(f"获取额外数据时出错: {e}")

    def _get_1905_movie_data(self):
        """从1905电影网获取电影数据"""
        try:
            url = "http://www.1905.com/mdb/film/list/o-3p-1.html"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')

            # 解析电影列表
            movie_items = soup.find_all('div', class_='pic-pack-outer')

            for item in movie_items[:20]:  # 限制数量
                movie_info = self._parse_1905_movie(item)
                if movie_info and not self._is_duplicate(movie_info):
                    self.movies_data.append(movie_info)

            logger.info(f"从1905电影网获取了 {len(movie_items[:20])} 部电影数据")

        except Exception as e:
            logger.error(f"获取1905电影网数据时出错: {e}")

    def _parse_1905_movie(self, item):
        """解析1905电影网的电影数据"""
        try:
            # 电影标题
            title_element = item.find('a')
            title = title_element.get('title', '') if title_element else ""

            # 其他信息可能需要进一步解析
            return {
                'title': title,
                'movie_id': '',
                'rank': 0,
                'rating': 0.0,
                'rating_people': 0,
                'year': 0,
                'director': '',
                'country': '',
                'genres': '',
                'quote': '',
                'source': '1905'
            }

        except Exception as e:
            logger.error(f"解析1905电影数据时出错: {e}")
            return None

    def _get_sina_movie_data(self):
        """从新浪娱乐获取电影数据"""
        try:
            # 新浪娱乐电影频道
            url = "http://ent.sina.com.cn/film/"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # 这里可以进一步解析新浪的电影数据
            logger.info("新浪娱乐数据获取完成")

        except Exception as e:
            logger.error(f"获取新浪娱乐数据时出错: {e}")

def main():
    """主函数"""
    collector = MovieDataCollector()

    try:
        # 采集豆瓣电影数据
        collector.get_douban_movies()

        # 获取时光网票房数据
        collector.get_mtime_box_office_data()

        # 获取额外的真实数据
        collector.get_additional_real_data()

        # 保存数据
        df = collector.save_data()

        # 显示数据概览
        if df is not None:
            logger.info("\n数据概览:")
            print(df.head())
            logger.info(f"\n数据形状: {df.shape}")
            logger.info(f"\n数据列: {list(df.columns)}")
            logger.info(f"\n数据来源统计:")
            print(df['source'].value_counts())

    except Exception as e:
        logger.error(f"主程序执行出错: {e}")

if __name__ == "__main__":
    main()
